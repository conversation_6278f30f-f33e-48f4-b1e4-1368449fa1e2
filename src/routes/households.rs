use actix_session::Session;
use actix_web::{web, HttpResponse, Result};
use diesel::prelude::*;
use serde::{Deserialize, Serialize};

use crate::models::household::{Household, NewHousehold};
use crate::models::user_household::{UserHousehold, NewUserHousehold};
use crate::models::user::User;
use crate::schema::{households, user_households, users};
use crate::utils::templates::{render_template_with_context};
use crate::utils::auth::is_authenticated;
use crate::DbPool;

#[derive(Deserialize)]
pub struct HouseholdForm {
    pub name: String,
    pub description: Option<String>,
    pub location: Option<String>,
}

#[derive(Deserialize)]
pub struct ShareHouseholdForm {
    pub username: String,
    pub role: String, // "admin", "member", "viewer"
}

#[derive(Serialize)]
pub struct HouseholdViewModel {
    pub id: i32,
    pub name: String,
    pub description: Option<String>,
    pub location: Option<String>,
    pub owner_id: i32,
    pub is_owner: bool,
    pub user_role: String,
    pub member_count: i64,
}

// List user's households
pub async fn list_households(session: Session, pool: web::Data<DbPool>) -> Result<HttpResponse> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_id = session.get::<i32>("user_id")?.unwrap();
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    // Get households where user is a member
    let user_households = user_households::table
        .filter(user_households::user_id.eq(user_id))
        .inner_join(households::table)
        .select((households::all_columns, user_households::role))
        .load::<(Household, String)>(&mut conn)
        .expect("Error loading user households");

    let mut household_view_models = Vec::new();

    for (household, role) in user_households {
        // Count members
        let member_count = user_households::table
            .filter(user_households::household_id.eq(household.id.unwrap()))
            .count()
            .get_result::<i64>(&mut conn)
            .unwrap_or(0);

        household_view_models.push(HouseholdViewModel {
            id: household.id.unwrap(),
            name: household.name,
            description: household.description,
            location: household.location,
            owner_id: household.owner_id,
            is_owner: household.owner_id == user_id,
            user_role: role,
            member_count,
        });
    }

    let mut ctx = tera::Context::new();
    ctx.insert("households", &household_view_models);

    render_template_with_context("households/list.html", &mut ctx, &session)
}

// Show form to create new household
pub async fn new_household_form(session: Session) -> Result<HttpResponse> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let mut ctx = tera::Context::new();
    render_template_with_context("households/new.html", &mut ctx, &session)
}

// Create new household
pub async fn create_household(
    session: Session,
    form: web::Form<HouseholdForm>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_id = session.get::<i32>("user_id")?.unwrap();
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    // Create household
    let new_household = NewHousehold {
        name: &form.name,
        description: form.description.as_deref(),
        location: form.location.as_deref(),
        owner_id: user_id,
    };

    let household_id = diesel::insert_into(households::table)
        .values(&new_household)
        .returning(households::id)
        .get_result::<Option<i32>>(&mut conn)
        .expect("Error creating household")
        .unwrap();

    // Add user as admin member
    let new_user_household = NewUserHousehold {
        user_id,
        household_id,
        role: "admin",
    };

    diesel::insert_into(user_households::table)
        .values(&new_user_household)
        .execute(&mut conn)
        .expect("Error adding user to household");

    Ok(HttpResponse::Found()
        .append_header(("Location", "/households"))
        .finish())
}

// View household details
pub async fn view_household(
    session: Session,
    path: web::Path<i32>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let household_id = path.into_inner();
    let user_id = session.get::<i32>("user_id")?.unwrap();
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    // Check if user has access to this household
    let user_household = user_households::table
        .filter(user_households::user_id.eq(user_id))
        .filter(user_households::household_id.eq(household_id))
        .first::<UserHousehold>(&mut conn)
        .optional()
        .expect("Error checking household access");

    if user_household.is_none() {
        return Ok(HttpResponse::Forbidden().body("Access denied"));
    }

    let household = households::table
        .find(household_id)
        .first::<Household>(&mut conn)
        .expect("Error loading household");

    // Get household members
    let members = user_households::table
        .filter(user_households::household_id.eq(household_id))
        .inner_join(users::table)
        .select((users::all_columns, user_households::role))
        .load::<(User, String)>(&mut conn)
        .expect("Error loading household members");

    let mut ctx = tera::Context::new();
    ctx.insert("household", &household);
    ctx.insert("members", &members);
    ctx.insert("user_role", &user_household.unwrap().role);
    ctx.insert("is_owner", &(household.owner_id == user_id));

    render_template_with_context("households/view.html", &mut ctx, &session)
}

pub fn init(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("/households")
            .route("", web::get().to(list_households))
            .route("/new", web::get().to(new_household_form))
            .route("/create", web::post().to(create_household))
            .route("/{id}/view", web::get().to(view_household)),
    );
}
