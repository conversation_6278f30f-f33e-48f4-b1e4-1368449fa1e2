pub mod auth;
pub mod plants;
pub mod plots;
pub mod seeds;
pub mod seasons;
pub mod season_plans;
pub mod admin;
pub mod notifications;
pub mod property;
pub mod households;
pub mod profile;

use actix_web::{web, HttpResponse, Result};
use actix_session::Session;
use crate::utils::templates::{render_template, render_template_with_context};

pub mod wizard;

pub fn init(cfg: &mut web::ServiceConfig) {
    plants::init(cfg);
    wizard::init(cfg);
    plots::init(cfg);
    seeds::init(cfg);
    seasons::init(cfg);
    season_plans::init(cfg);
    notifications::init(cfg);
    admin::init(cfg);
    auth::init(cfg);
    property::init(cfg);
    households::init(cfg);
    profile::init(cfg);

    cfg.route("/", web::get().to(index));
}


async fn index(session: Session) -> Result<HttpResponse> {
    let mut ctx = tera::Context::new();
    Ok(render_template_with_context("index.html", &mut ctx, &session)?)
}
/*
async fn not_found_handler() -> Result<HttpResponse> {
    Ok(HttpResponse::NotFound().body("404 Page not found"))
}
*/