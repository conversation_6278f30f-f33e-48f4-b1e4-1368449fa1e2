<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>{% block title %}Gardening App{% endblock %}</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <link rel="stylesheet" href="/static/css/wizard.css">
    <script src="https://unpkg.com/htmx.org@1.9.2"></script>
    <script src="/static/js/scripts.js" defer></script>
    {% block head %}{% endblock %}
</head>
<body class="bg-white dark:bg-gray-900 text-gray-800 dark:text-gray-100">
<nav class="bg-green-800 text-white p-4">
    <div class="container mx-auto flex justify-between items-center">
        <a href="/" class="text-xl font-bold">Garden Planner</a>
        <div class="space-x-4">
            {% if user_context.is_authenticated %}
                <a href="/plants/list" class="hover:text-green-200">Plants</a>
                <a href="/seeds/list" class="hover:text-green-200">Seeds</a>
                <a href="/plots/list" class="hover:text-green-200">Plots</a>
                <a href="/property" class="hover:text-green-200">Properties</a>
                <a href="/seasons/list" class="hover:text-green-200">Seasons</a>
                <a href="/season_plans" class="hover:text-green-200">Season Plans</a>
                <a href="/notifications/list" class="hover:text-green-200">Notifications</a>
                {% if user_context.is_admin %}
                    <a href="/admin" class="hover:text-green-200">Admin</a>
                {% endif %}
                <div class="relative inline-block">
                    <button id="user-menu-button" class="flex items-center space-x-2 text-green-200 hover:text-white">
                        <div class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                            {{ user_context.username | first | upper }}
                        </div>
                        <span>{{ user_context.username }}</span>
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div id="user-menu" class="hidden absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg py-1 z-50">
                        <a href="/profile" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">Profile</a>
                        <a href="/settings" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">Settings</a>
                        <a href="/households" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">Households</a>
                        <hr class="my-1">
                        <a href="/auth/logout" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">Logout</a>
                    </div>
                </div>
            {% else %}
                <a href="/auth/register" class="hover:text-green-200">Register</a>
                <a href="/auth/login" class="hover:text-green-200">Login</a>
            {% endif %}
        </div>
    </div>
</nav>
<main class="container mx-auto px-4 py-6">
    {% block content %}{% endblock %}
</main>

<script>
// User menu dropdown functionality
document.addEventListener('DOMContentLoaded', function() {
    const userMenuButton = document.getElementById('user-menu-button');
    const userMenu = document.getElementById('user-menu');

    if (userMenuButton && userMenu) {
        userMenuButton.addEventListener('click', function(e) {
            e.stopPropagation();
            userMenu.classList.toggle('hidden');
        });

        // Close menu when clicking outside
        document.addEventListener('click', function() {
            userMenu.classList.add('hidden');
        });

        // Prevent menu from closing when clicking inside it
        userMenu.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    }
});
</script>
</body>
</html>
