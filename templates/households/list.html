{% extends "base.html" %}
{% block title %}My Households{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold">My Households</h1>
        <a href="/households/new" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded">
            Create New Household
        </a>
    </div>

    {% if households %}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {% for household in households %}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-semibold">{{ household.name }}</h2>
                {% if household.is_owner %}
                    <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">Owner</span>
                {% else %}
                    <span class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded">{{ household.user_role | title }}</span>
                {% endif %}
            </div>
            
            {% if household.description %}
            <p class="text-gray-600 dark:text-gray-300 mb-3">{{ household.description }}</p>
            {% endif %}
            
            {% if household.location %}
            <p class="text-sm text-gray-500 dark:text-gray-400 mb-3">
                <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                {{ household.location }}
            </p>
            {% endif %}
            
            <div class="flex items-center justify-between">
                <span class="text-sm text-gray-500 dark:text-gray-400">
                    {{ household.member_count }} member{% if household.member_count != 1 %}s{% endif %}
                </span>
                <a href="/households/{{ household.id }}/view" class="text-green-600 hover:text-green-800 font-medium">
                    View Details →
                </a>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No households</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Get started by creating your first household.</p>
        <div class="mt-6">
            <a href="/households/new" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Create Household
            </a>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
